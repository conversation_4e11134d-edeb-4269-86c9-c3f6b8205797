2025-07-17 14:54:17,530 p=64070 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:54:17,534 p=64070 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:54:17,604 p=64070 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:54:17,639 p=64070 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:54:17,651 p=64070 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:54:17,666 p=64070 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:54:17,666 p=64070 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:54:17,699 p=64070 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:54:17,700 p=64070 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:54:17,700 p=64070 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:54:18,145 p=64070 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:54:18,146 p=64070 u=daddasoft n=ansible | failed: [localhost -> vps5] (item={'key': 'pharmalien.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "item": {"key": "pharmalien.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}
2025-07-17 14:54:18,554 p=64070 u=daddasoft n=ansible | failed: [localhost -> vps5] (item={'key': 'webfixgroupe.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}
2025-07-17 14:54:18,554 p=64070 u=daddasoft n=ansible | fatal: [localhost -> {{ item.value }}]: UNREACHABLE! => {"changed": false, "msg": "All items completed", "results": [{"ansible_loop_var": "item", "item": {"key": "pharmalien.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}, {"ansible_loop_var": "item", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}], "unreachable": true}
2025-07-17 14:54:18,555 p=64070 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:54:18,555 p=64070 u=daddasoft n=ansible | localhost                  : ok=3    changed=1    unreachable=1    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 14:54:46,408 p=64217 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:54:46,412 p=64217 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:54:46,480 p=64217 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:54:46,512 p=64217 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:54:46,523 p=64217 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:54:46,538 p=64217 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:54:46,538 p=64217 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:54:46,569 p=64217 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:54:46,569 p=64217 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:54:46,570 p=64217 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:54:47,003 p=64217 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:54:47,004 p=64217 u=daddasoft n=ansible | failed: [localhost -> vps5] (item={'key': 'pharmalien.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "item": {"key": "pharmalien.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}
2025-07-17 14:54:47,410 p=64217 u=daddasoft n=ansible | failed: [localhost -> vps5] (item={'key': 'webfixgroupe.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}
2025-07-17 14:54:47,412 p=64217 u=daddasoft n=ansible | fatal: [localhost -> {{ item.value }}]: UNREACHABLE! => {"changed": false, "msg": "All items completed", "results": [{"ansible_loop_var": "item", "item": {"key": "pharmalien.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}, {"ansible_loop_var": "item", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}], "unreachable": true}
2025-07-17 14:54:47,413 p=64217 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:54:47,413 p=64217 u=daddasoft n=ansible | localhost                  : ok=3    changed=1    unreachable=1    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 14:55:04,263 p=64319 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:55:04,266 p=64319 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:55:04,334 p=64319 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:55:04,365 p=64319 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:55:04,376 p=64319 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:55:04,390 p=64319 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:55:04,391 p=64319 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:55:04,422 p=64319 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:55:04,423 p=64319 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:55:04,423 p=64319 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:55:09,149 p=64319 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:55:09,149 p=64319 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:55:11,532 p=64319 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:55:11,533 p=64319 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but future installation of another
Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 14:55:11,556 p=64319 u=daddasoft n=ansible | TASK [sync_produit : Wait for all async jobs to finish] ************************************************************************************************
2025-07-17 14:55:11,556 p=64319 u=daddasoft n=ansible | fatal: [localhost -> {{ item._ansible_item_result.delegate_to }}]: FAILED! => {"msg": "'dict object' has no attribute '_ansible_item_result'"}
2025-07-17 14:55:11,557 p=64319 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:55:11,557 p=64319 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 14:55:49,782 p=64572 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:55:49,785 p=64572 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:55:49,855 p=64572 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:55:49,887 p=64572 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:55:49,899 p=64572 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:55:49,914 p=64572 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:55:49,915 p=64572 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:55:49,949 p=64572 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:55:49,949 p=64572 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:55:49,949 p=64572 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:55:55,289 p=64572 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:55:55,289 p=64572 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:55:57,469 p=64572 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:55:57,471 p=64572 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but future installation of another
Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 14:55:57,494 p=64572 u=daddasoft n=ansible | TASK [sync_produit : Wait for all async jobs to finish] ************************************************************************************************
2025-07-17 14:55:57,494 p=64572 u=daddasoft n=ansible | An exception occurred during task execution. To see the full traceback, use -vvv. The error was: TypeError: unhashable type: 'dict'
2025-07-17 14:55:57,495 p=64572 u=daddasoft n=ansible | fatal: [localhost -> {{ item }}]: FAILED! => {"msg": "Unexpected failure during module execution: unhashable type: 'dict'", "stdout": ""}
2025-07-17 14:55:57,495 p=64572 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:55:57,495 p=64572 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 14:56:31,055 p=64774 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:56:31,059 p=64774 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:56:31,133 p=64774 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:56:31,165 p=64774 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:56:31,176 p=64774 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:56:31,192 p=64774 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:56:31,192 p=64774 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:56:31,226 p=64774 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:56:31,226 p=64774 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:56:31,226 p=64774 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:56:33,458 p=64774 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:56:33,458 p=64774 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:56:35,611 p=64774 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:56:35,612 p=64774 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but future installation of another
Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 14:56:35,804 p=64774 u=daddasoft n=ansible | TASK [sync_produit : Wait for all async jobs to finish] ************************************************************************************************
2025-07-17 14:56:35,805 p=64774 u=daddasoft n=ansible | failed: [localhost] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j679483150211.1056462', 'results_file': '/home/<USER>/.ansible_async/j679483150211.1056462', 'changed': True, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j679483150211.1056462", "ansible_loop_var": "item", "changed": false, "finished": 1, "item": {"ansible_job_id": "j679483150211.1056462", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "pharmalien.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j679483150211.1056462", "started": 1}, "msg": "could not find job", "results_file": "/home/<USER>/.ansible_async/j679483150211.1056462", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 14:56:35,910 p=64774 u=daddasoft n=ansible | failed: [localhost] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j840458782478.1056535', 'results_file': '/home/<USER>/.ansible_async/j840458782478.1056535', 'changed': True, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j840458782478.1056535", "ansible_loop_var": "item", "changed": false, "finished": 1, "item": {"ansible_job_id": "j840458782478.1056535", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j840458782478.1056535", "started": 1}, "msg": "could not find job", "results_file": "/home/<USER>/.ansible_async/j840458782478.1056535", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 14:56:35,911 p=64774 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:56:35,911 p=64774 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 14:57:07,358 p=64988 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:57:07,361 p=64988 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:57:07,433 p=64988 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:57:07,464 p=64988 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:57:07,476 p=64988 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:57:07,492 p=64988 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:57:07,492 p=64988 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:57:07,526 p=64988 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:57:07,526 p=64988 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:57:07,526 p=64988 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:57:09,938 p=64988 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:57:09,938 p=64988 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:57:12,265 p=64988 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:57:12,266 p=64988 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but future installation of another
Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 14:57:12,471 p=64988 u=daddasoft n=ansible | TASK [sync_produit : Wait for all async jobs to finish] ************************************************************************************************
2025-07-17 14:57:12,471 p=64988 u=daddasoft n=ansible | failed: [localhost] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j856306452467.1056972', 'results_file': '/home/<USER>/.ansible_async/j856306452467.1056972', 'changed': True, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j856306452467.1056972", "ansible_loop_var": "item", "changed": false, "finished": 1, "item": {"ansible_job_id": "j856306452467.1056972", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "pharmalien.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j856306452467.1056972", "started": 1}, "msg": "could not find job", "results_file": "/home/<USER>/.ansible_async/j856306452467.1056972", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 14:57:12,572 p=64988 u=daddasoft n=ansible | failed: [localhost] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j152785213864.1057068', 'results_file': '/home/<USER>/.ansible_async/j152785213864.1057068', 'changed': True, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j152785213864.1057068", "ansible_loop_var": "item", "changed": false, "finished": 1, "item": {"ansible_job_id": "j152785213864.1057068", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j152785213864.1057068", "started": 1}, "msg": "could not find job", "results_file": "/home/<USER>/.ansible_async/j152785213864.1057068", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 14:57:12,574 p=64988 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:57:12,574 p=64988 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 14:58:06,676 p=65271 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 14:58:06,680 p=65271 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be removed in 
version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-17 14:58:06,752 p=65271 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************************************************
2025-07-17 14:58:06,785 p=65271 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************************************************
2025-07-17 14:58:06,797 p=65271 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 14:58:06,813 p=65271 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************************************************
2025-07-17 14:58:06,813 p=65271 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 14:58:06,851 p=65271 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************************************************
2025-07-17 14:58:06,851 p=65271 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:58:06,852 p=65271 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:58:09,195 p=65271 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************************************************
2025-07-17 14:58:09,195 p=65271 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 14:58:14,718 p=65271 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 14:58:14,720 p=65271 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but future installation of another
Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 14:58:14,744 p=65271 u=daddasoft n=ansible | TASK [sync_produit : Wait for all async jobs to finish] ************************************************************************************************
2025-07-17 14:58:14,744 p=65271 u=daddasoft n=ansible | fatal: [localhost -> {{ item._ansible_item_result.delegate_to }}]: FAILED! => {"msg": "'dict object' has no attribute '_ansible_item_result'"}
2025-07-17 14:58:14,745 p=65271 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************************************************
2025-07-17 14:58:14,745 p=65271 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 15:02:30,510 p=66877 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:02:30,513 p=66877 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:02:30,585 p=66877 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:02:30,619 p=66877 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:02:30,630 p=66877 u=daddasoft n=ansible | ERROR! no module/action detected in task.

The error appears to be in '/home/<USER>/play-deomo/roles/sync_produit/tasks/main.yml': line 41, column 3, but may
be elsewhere in the file depending on the exact syntax problem.

The offending line appears to be:


- name :
  ^ here

2025-07-17 15:02:37,034 p=66962 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:02:37,037 p=66962 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:02:37,111 p=66962 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:02:37,141 p=66962 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:02:37,153 p=66962 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:02:37,169 p=66962 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:02:37,169 p=66962 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:02:37,207 p=66962 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:02:37,207 p=66962 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:02:37,208 p=66962 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:03:10,639 p=66962 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:03:10,639 p=66962 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:03:34,816 p=66962 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:03:34,817 p=66962 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:03:34,833 p=66962 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:03:34,833 p=66962 u=daddasoft n=ansible | fatal: [localhost]: FAILED! => {"msg": "'script_results' is undefined"}
2025-07-17 15:03:34,834 p=66962 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:03:34,834 p=66962 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 15:26:55,523 p=71096 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:26:55,530 p=71096 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:26:55,674 p=71096 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:26:55,728 p=71096 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:26:55,743 p=71096 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:26:55,761 p=71096 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:26:55,762 p=71096 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:26:55,809 p=71096 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:26:55,810 p=71096 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:26:55,810 p=71096 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:27:30,885 p=71096 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:27:30,885 p=71096 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:27:54,488 p=71096 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:27:54,489 p=71096 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:27:54,528 p=71096 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:27:54,528 p=71096 u=daddasoft n=ansible | ok: [localhost] => (item={'changed': True, 'stdout': 'receiving incremental file list\n\nsent 20 bytes  received 70 bytes  25.71 bytes/sec\ntotal size is 32,970,249  speedup is 366,336.10\nreceiving incremental file list\n\nsent 20 bytes  received 69 bytes  178.00 bytes/sec\ntotal size is 359,184  speedup is 4,035.78\nreceiving incremental file list\n\nsent 20 bytes  received 73 bytes  62.00 bytes/sec\ntotal size is 6,772,013  speedup is 72,817.34\nBEGIN\nTRUNCATE TABLE\nTRUNCATE TABLE\nCOPY 3540\nCOPY 153057\nUPDATE 3540\nINSERT 0 0\nUPDATE 153057\nINSERT 0 0\nUPDATE 51278\nUPDATE 14\nINSERT 0 0\nUPDATE 126\nINSERT 0 0\nUPDATE 0\nINSERT 0 0\nUPDATE 16\nINSERT 0 0\nINSERT 0 0\nUPDATE 153057\nDROP TABLE\nCREATE TABLE\nTRUNCATE TABLE\nCOPY 123904\nTRUNCATE TABLE\nINSERT 0 123904\nINSERT 0 6480\nCOMMIT', 'stderr': 'psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table "transco_produit_temp" does not exist, skipping', 'rc': 0, 'cmd': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', 'start': '2025-07-17 14:27:00.964671', 'end': '2025-07-17 14:27:32.313071', 'delta': '0:00:31.348400', 'msg': '', 'invocation': {'module_args': {'_raw_params': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': ['receiving incremental file list', '', 'sent 20 bytes  received 70 bytes  25.71 bytes/sec', 'total size is 32,970,249  speedup is 366,336.10', 'receiving incremental file list', '', 'sent 20 bytes  received 69 bytes  178.00 bytes/sec', 'total size is 359,184  speedup is 4,035.78', 'receiving incremental file list', '', 'sent 20 bytes  received 73 bytes  62.00 bytes/sec', 'total size is 6,772,013  speedup is 72,817.34', 'BEGIN', 'TRUNCATE TABLE', 'TRUNCATE TABLE', 'COPY 3540', 'COPY 153057', 'UPDATE 3540', 'INSERT 0 0', 'UPDATE 153057', 'INSERT 0 0', 'UPDATE 51278', 'UPDATE 14', 'INSERT 0 0', 'UPDATE 126', 'INSERT 0 0', 'UPDATE 0', 'INSERT 0 0', 'UPDATE 16', 'INSERT 0 0', 'INSERT 0 0', 'UPDATE 153057', 'DROP TABLE', 'CREATE TABLE', 'TRUNCATE TABLE', 'COPY 123904', 'TRUNCATE TABLE', 'INSERT 0 123904', 'INSERT 0 6480', 'COMMIT'], 'stderr_lines': ['psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table "transco_produit_temp" does not exist, skipping'], 'failed': False, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {
    "msg": "pharmalien.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:27:54,545 p=71096 u=daddasoft n=ansible | ok: [localhost] => (item={'changed': True, 'stdout': 'receiving incremental file list\n\nsent 20 bytes  received 70 bytes  60.00 bytes/sec\ntotal size is 32,970,249  speedup is 366,336.10\nreceiving incremental file list\n\nsent 20 bytes  received 69 bytes  59.33 bytes/sec\ntotal size is 359,184  speedup is 4,035.78\nreceiving incremental file list\n\nsent 20 bytes  received 73 bytes  62.00 bytes/sec\ntotal size is 6,772,013  speedup is 72,817.34\nBEGIN\nTRUNCATE TABLE\nCOPY 3540\nCOPY 153057\nUPDATE 3540\nINSERT 0 0\nUPDATE 153057\nINSERT 0 0\nUPDATE 51359\nDROP TABLE\nCREATE TABLE\nTRUNCATE TABLE\nCOPY 123904\nTRUNCATE TABLE\nINSERT 0 123904\nCOMMIT', 'stderr': 'psql:/db_dump/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sql:111: NOTICE:  table "transco_produit_temp" does not exist, skipping', 'rc': 0, 'cmd': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sh', 'start': '2025-07-17 14:27:33.382200', 'end': '2025-07-17 14:27:54.868788', 'delta': '0:00:21.486588', 'msg': '', 'invocation': {'module_args': {'_raw_params': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sh', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': ['receiving incremental file list', '', 'sent 20 bytes  received 70 bytes  60.00 bytes/sec', 'total size is 32,970,249  speedup is 366,336.10', 'receiving incremental file list', '', 'sent 20 bytes  received 69 bytes  59.33 bytes/sec', 'total size is 359,184  speedup is 4,035.78', 'receiving incremental file list', '', 'sent 20 bytes  received 73 bytes  62.00 bytes/sec', 'total size is 6,772,013  speedup is 72,817.34', 'BEGIN', 'TRUNCATE TABLE', 'COPY 3540', 'COPY 153057', 'UPDATE 3540', 'INSERT 0 0', 'UPDATE 153057', 'INSERT 0 0', 'UPDATE 51359', 'DROP TABLE', 'CREATE TABLE', 'TRUNCATE TABLE', 'COPY 123904', 'TRUNCATE TABLE', 'INSERT 0 123904', 'COMMIT'], 'stderr_lines': ['psql:/db_dump/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sql:111: NOTICE:  table "transco_produit_temp" does not exist, skipping'], 'failed': False, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {
    "msg": "webfixgroupe.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:27:54,553 p=71096 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:27:54,553 p=71096 u=daddasoft n=ansible | localhost                  : ok=5    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 15:29:22,896 p=71694 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:29:22,900 p=71694 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:29:22,969 p=71694 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:29:23,000 p=71694 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:29:23,012 p=71694 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:29:23,028 p=71694 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:29:23,029 p=71694 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:29:23,063 p=71694 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:29:23,063 p=71694 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:29:23,064 p=71694 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:29:54,278 p=71694 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:29:54,278 p=71694 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:29:55,378 p=71694 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:29:55,378 p=71694 u=daddasoft n=ansible | failed: [localhost -> vps5(**************)] (item={'key': 'webfixgroupe.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "changed": true, "cmd": "exit 1", "delta": "0:00:00.005423", "end": "2025-07-17 14:29:57.164739", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "non-zero return code", "rc": 1, "start": "2025-07-17 14:29:57.159316", "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 15:29:55,380 p=71694 u=daddasoft n=ansible | ...ignoring
2025-07-17 15:29:55,408 p=71694 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:29:55,411 p=71694 u=daddasoft n=ansible | ok: [localhost] => (item={'changed': True, 'stdout': 'receiving incremental file list\n\nsent 20 bytes  received 70 bytes  60.00 bytes/sec\ntotal size is 32,970,249  speedup is 366,336.10\nreceiving incremental file list\n\nsent 20 bytes  received 69 bytes  59.33 bytes/sec\ntotal size is 359,184  speedup is 4,035.78\nreceiving incremental file list\n\nsent 20 bytes  received 73 bytes  62.00 bytes/sec\ntotal size is 6,772,013  speedup is 72,817.34\nBEGIN\nTRUNCATE TABLE\nTRUNCATE TABLE\nCOPY 3540\nCOPY 153057\nUPDATE 3540\nINSERT 0 0\nUPDATE 153057\nINSERT 0 0\nUPDATE 51278\nUPDATE 14\nINSERT 0 0\nUPDATE 126\nINSERT 0 0\nUPDATE 0\nINSERT 0 0\nUPDATE 16\nINSERT 0 0\nINSERT 0 0\nUPDATE 153057\nDROP TABLE\nCREATE TABLE\nTRUNCATE TABLE\nCOPY 123904\nTRUNCATE TABLE\nINSERT 0 123904\nINSERT 0 6480\nCOMMIT', 'stderr': 'psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table "transco_produit_temp" does not exist, skipping', 'rc': 0, 'cmd': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', 'start': '2025-07-17 14:29:28.341841', 'end': '2025-07-17 14:29:55.961202', 'delta': '0:00:27.619361', 'msg': '', 'invocation': {'module_args': {'_raw_params': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': ['receiving incremental file list', '', 'sent 20 bytes  received 70 bytes  60.00 bytes/sec', 'total size is 32,970,249  speedup is 366,336.10', 'receiving incremental file list', '', 'sent 20 bytes  received 69 bytes  59.33 bytes/sec', 'total size is 359,184  speedup is 4,035.78', 'receiving incremental file list', '', 'sent 20 bytes  received 73 bytes  62.00 bytes/sec', 'total size is 6,772,013  speedup is 72,817.34', 'BEGIN', 'TRUNCATE TABLE', 'TRUNCATE TABLE', 'COPY 3540', 'COPY 153057', 'UPDATE 3540', 'INSERT 0 0', 'UPDATE 153057', 'INSERT 0 0', 'UPDATE 51278', 'UPDATE 14', 'INSERT 0 0', 'UPDATE 126', 'INSERT 0 0', 'UPDATE 0', 'INSERT 0 0', 'UPDATE 16', 'INSERT 0 0', 'INSERT 0 0', 'UPDATE 153057', 'DROP TABLE', 'CREATE TABLE', 'TRUNCATE TABLE', 'COPY 123904', 'TRUNCATE TABLE', 'INSERT 0 123904', 'INSERT 0 6480', 'COMMIT'], 'stderr_lines': ['psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table "transco_produit_temp" does not exist, skipping'], 'failed': False, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {
    "msg": "pharmalien.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:29:55,417 p=71694 u=daddasoft n=ansible | ok: [localhost] => (item={'changed': True, 'stdout': '', 'stderr': '', 'rc': 1, 'cmd': 'exit 1', 'start': '2025-07-17 14:29:57.159316', 'end': '2025-07-17 14:29:57.164739', 'delta': '0:00:00.005423', 'failed': True, 'msg': 'non-zero return code', 'invocation': {'module_args': {'_raw_params': 'exit 1', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': [], 'stderr_lines': [], 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {
    "msg": "webfixgroupe.dev on vps5 ❌ FAILED"
}
2025-07-17 15:29:55,424 p=71694 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:29:55,424 p=71694 u=daddasoft n=ansible | localhost                  : ok=5    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=1   
2025-07-17 15:31:37,353 p=72248 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:31:37,356 p=72248 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:31:37,427 p=72248 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:31:37,457 p=72248 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:31:37,469 p=72248 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:31:37,484 p=72248 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:31:37,485 p=72248 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:31:37,518 p=72248 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:31:37,518 p=72248 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:31:37,518 p=72248 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:32:16,280 p=72248 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:32:16,280 p=72248 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:32:43,807 p=72248 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:32:43,808 p=72248 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:32:43,838 p=72248 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:32:43,838 p=72248 u=daddasoft n=ansible | ok: [localhost] => (item=None)
2025-07-17 15:32:43,842 p=72248 u=daddasoft n=ansible | ok: [localhost] => (item=None)
2025-07-17 15:32:43,844 p=72248 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 15:32:43,850 p=72248 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:32:43,851 p=72248 u=daddasoft n=ansible | localhost                  : ok=5    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 15:33:10,824 p=72644 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:33:10,827 p=72644 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:33:10,911 p=72644 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:33:10,948 p=72644 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:33:10,961 p=72644 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:33:10,979 p=72644 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:33:10,980 p=72644 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:33:11,015 p=72644 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:33:11,016 p=72644 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:33:11,016 p=72644 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:33:44,850 p=72644 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:33:44,851 p=72644 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:34:10,132 p=72644 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:34:10,133 p=72644 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:34:10,153 p=72644 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:34:10,153 p=72644 u=daddasoft n=ansible | fatal: [localhost]: FAILED! => {"msg": "could not find 'results' key in iterated item {'changed': True, 'stdout': 'receiving incremental file list\\n\\nsent 20 bytes  received 70 bytes  25.71 bytes/sec\\ntotal size is 32,970,249  speedup is 366,336.10\\nreceiving incremental file list\\n\\nsent 20 bytes  received 69 bytes  59.33 bytes/sec\\ntotal size is 359,184  speedup is 4,035.78\\nreceiving incremental file list\\n\\nsent 20 bytes  received 73 bytes  62.00 bytes/sec\\ntotal size is 6,772,013  speedup is 72,817.34\\nBEGIN\\nTRUNCATE TABLE\\nTRUNCATE TABLE\\nCOPY 3540\\nCOPY 153057\\nUPDATE 3540\\nINSERT 0 0\\nUPDATE 153057\\nINSERT 0 0\\nUPDATE 51278\\nUPDATE 14\\nINSERT 0 0\\nUPDATE 126\\nINSERT 0 0\\nUPDATE 0\\nINSERT 0 0\\nUPDATE 16\\nINSERT 0 0\\nINSERT 0 0\\nUPDATE 153057\\nDROP TABLE\\nCREATE TABLE\\nTRUNCATE TABLE\\nCOPY 123904\\nTRUNCATE TABLE\\nINSERT 0 123904\\nINSERT 0 6480\\nCOMMIT', 'stderr': 'psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table \"transco_produit_temp\" does not exist, skipping', 'rc': 0, 'cmd': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', 'start': '2025-07-17 14:33:12.442940', 'end': '2025-07-17 14:33:45.085050', 'delta': '0:00:32.642110', 'msg': '', 'invocation': {'module_args': {'_raw_params': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': ['receiving incremental file list', '', 'sent 20 bytes  received 70 bytes  25.71 bytes/sec', 'total size is 32,970,249  speedup is 366,336.10', 'receiving incremental file list', '', 'sent 20 bytes  received 69 bytes  59.33 bytes/sec', 'total size is 359,184  speedup is 4,035.78', 'receiving incremental file list', '', 'sent 20 bytes  received 73 bytes  62.00 bytes/sec', 'total size is 6,772,013  speedup is 72,817.34', 'BEGIN', 'TRUNCATE TABLE', 'TRUNCATE TABLE', 'COPY 3540', 'COPY 153057', 'UPDATE 3540', 'INSERT 0 0', 'UPDATE 153057', 'INSERT 0 0', 'UPDATE 51278', 'UPDATE 14', 'INSERT 0 0', 'UPDATE 126', 'INSERT 0 0', 'UPDATE 0', 'INSERT 0 0', 'UPDATE 16', 'INSERT 0 0', 'INSERT 0 0', 'UPDATE 153057', 'DROP TABLE', 'CREATE TABLE', 'TRUNCATE TABLE', 'COPY 123904', 'TRUNCATE TABLE', 'INSERT 0 123904', 'INSERT 0 6480', 'COMMIT'], 'stderr_lines': ['psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table \"transco_produit_temp\" does not exist, skipping'], 'failed': False, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}"}
2025-07-17 15:34:10,154 p=72644 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:34:10,154 p=72644 u=daddasoft n=ansible | localhost                  : ok=4    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 15:41:42,232 p=74357 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:41:42,236 p=74357 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:41:42,324 p=74357 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:41:42,359 p=74357 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:41:42,373 p=74357 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:41:42,393 p=74357 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:41:42,394 p=74357 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:41:42,440 p=74357 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:41:42,440 p=74357 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:41:42,440 p=74357 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:42:15,787 p=74357 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:42:15,788 p=74357 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:42:39,033 p=74357 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:42:39,034 p=74357 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:42:39,066 p=74357 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:42:39,067 p=74357 u=daddasoft n=ansible | ok: [localhost] => (item={'changed': True, 'stdout': 'receiving incremental file list\n\nsent 20 bytes  received 70 bytes  60.00 bytes/sec\ntotal size is 32,970,249  speedup is 366,336.10\nreceiving incremental file list\n\nsent 20 bytes  received 69 bytes  59.33 bytes/sec\ntotal size is 359,184  speedup is 4,035.78\nreceiving incremental file list\n\nsent 20 bytes  received 73 bytes  62.00 bytes/sec\ntotal size is 6,772,013  speedup is 72,817.34\nBEGIN\nTRUNCATE TABLE\nTRUNCATE TABLE\nCOPY 3540\nCOPY 153057\nUPDATE 3540\nINSERT 0 0\nUPDATE 153057\nINSERT 0 0\nUPDATE 51278\nUPDATE 14\nINSERT 0 0\nUPDATE 126\nINSERT 0 0\nUPDATE 0\nINSERT 0 0\nUPDATE 16\nINSERT 0 0\nINSERT 0 0\nUPDATE 153057\nDROP TABLE\nCREATE TABLE\nTRUNCATE TABLE\nCOPY 123904\nTRUNCATE TABLE\nINSERT 0 123904\nINSERT 0 6480\nCOMMIT', 'stderr': 'psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table "transco_produit_temp" does not exist, skipping', 'rc': 0, 'cmd': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', 'start': '2025-07-17 14:41:46.306699', 'end': '2025-07-17 14:42:16.070212', 'delta': '0:00:29.763513', 'msg': '', 'invocation': {'module_args': {'_raw_params': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': ['receiving incremental file list', '', 'sent 20 bytes  received 70 bytes  60.00 bytes/sec', 'total size is 32,970,249  speedup is 366,336.10', 'receiving incremental file list', '', 'sent 20 bytes  received 69 bytes  59.33 bytes/sec', 'total size is 359,184  speedup is 4,035.78', 'receiving incremental file list', '', 'sent 20 bytes  received 73 bytes  62.00 bytes/sec', 'total size is 6,772,013  speedup is 72,817.34', 'BEGIN', 'TRUNCATE TABLE', 'TRUNCATE TABLE', 'COPY 3540', 'COPY 153057', 'UPDATE 3540', 'INSERT 0 0', 'UPDATE 153057', 'INSERT 0 0', 'UPDATE 51278', 'UPDATE 14', 'INSERT 0 0', 'UPDATE 126', 'INSERT 0 0', 'UPDATE 0', 'INSERT 0 0', 'UPDATE 16', 'INSERT 0 0', 'INSERT 0 0', 'UPDATE 153057', 'DROP TABLE', 'CREATE TABLE', 'TRUNCATE TABLE', 'COPY 123904', 'TRUNCATE TABLE', 'INSERT 0 123904', 'INSERT 0 6480', 'COMMIT'], 'stderr_lines': ['psql:/db_dump/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sql:219: NOTICE:  table "transco_produit_temp" does not exist, skipping'], 'failed': False, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {
    "msg": "pharmalien.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:42:39,071 p=74357 u=daddasoft n=ansible | ok: [localhost] => (item={'changed': True, 'stdout': 'receiving incremental file list\n\nsent 20 bytes  received 70 bytes  36.00 bytes/sec\ntotal size is 32,970,249  speedup is 366,336.10\nreceiving incremental file list\n\nsent 20 bytes  received 69 bytes  59.33 bytes/sec\ntotal size is 359,184  speedup is 4,035.78\nreceiving incremental file list\n\nsent 20 bytes  received 73 bytes  186.00 bytes/sec\ntotal size is 6,772,013  speedup is 72,817.34\nBEGIN\nTRUNCATE TABLE\nCOPY 3540\nCOPY 153057\nUPDATE 3540\nINSERT 0 0\nUPDATE 153057\nINSERT 0 0\nUPDATE 51359\nDROP TABLE\nCREATE TABLE\nTRUNCATE TABLE\nCOPY 123904\nTRUNCATE TABLE\nINSERT 0 123904\nCOMMIT', 'stderr': 'psql:/db_dump/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sql:111: NOTICE:  table "transco_produit_temp" does not exist, skipping', 'rc': 0, 'cmd': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sh', 'start': '2025-07-17 14:42:17.481655', 'end': '2025-07-17 14:42:41.551151', 'delta': '0:00:24.069496', 'msg': '', 'invocation': {'module_args': {'_raw_params': '/home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sh', '_uses_shell': True, 'expand_argument_vars': True, 'stdin_add_newline': True, 'strip_empty_ends': True, 'argv': None, 'chdir': None, 'executable': None, 'creates': None, 'removes': None, 'stdin': None}}, 'stdout_lines': ['receiving incremental file list', '', 'sent 20 bytes  received 70 bytes  36.00 bytes/sec', 'total size is 32,970,249  speedup is 366,336.10', 'receiving incremental file list', '', 'sent 20 bytes  received 69 bytes  59.33 bytes/sec', 'total size is 359,184  speedup is 4,035.78', 'receiving incremental file list', '', 'sent 20 bytes  received 73 bytes  186.00 bytes/sec', 'total size is 6,772,013  speedup is 72,817.34', 'BEGIN', 'TRUNCATE TABLE', 'COPY 3540', 'COPY 153057', 'UPDATE 3540', 'INSERT 0 0', 'UPDATE 153057', 'INSERT 0 0', 'UPDATE 51359', 'DROP TABLE', 'CREATE TABLE', 'TRUNCATE TABLE', 'COPY 123904', 'TRUNCATE TABLE', 'INSERT 0 123904', 'COMMIT'], 'stderr_lines': ['psql:/db_dump/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sql:111: NOTICE:  table "transco_produit_temp" does not exist, skipping'], 'failed': False, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {
    "msg": "webfixgroupe.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:42:39,079 p=74357 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:42:39,080 p=74357 u=daddasoft n=ansible | localhost                  : ok=5    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 15:44:52,872 p=75024 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:44:52,877 p=75024 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:44:52,957 p=75024 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:44:52,991 p=75024 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:44:53,006 p=75024 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:44:53,025 p=75024 u=daddasoft n=ansible | TASK [sync_produit : test message] *********************************************************************************
2025-07-17 15:44:53,026 p=75024 u=daddasoft n=ansible | ok: [localhost] => {
    "msg": "Hello World!"
}
2025-07-17 15:44:53,067 p=75024 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:44:53,067 p=75024 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:44:53,068 p=75024 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:45:30,973 p=75024 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password] *****************************************************
2025-07-17 15:45:30,973 p=75024 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:45:55,989 p=75024 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:45:55,990 p=75024 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:45:56,023 p=75024 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:45:56,023 p=75024 u=daddasoft n=ansible | ok: [localhost] => (item=pharmalien.dev) => {
    "msg": "pharmalien.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:45:56,028 p=75024 u=daddasoft n=ansible | ok: [localhost] => (item=webfixgroupe.dev) => {
    "msg": "webfixgroupe.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:45:56,037 p=75024 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:45:56,037 p=75024 u=daddasoft n=ansible | localhost                  : ok=5    changed=2    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 15:55:35,917 p=77341 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:55:35,920 p=77341 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:55:35,994 p=77341 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:55:36,026 p=77341 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:55:36,043 p=77341 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:55:36,068 p=77341 u=daddasoft n=ansible | TASK [sync_produit : Set Slack webhook URL from environment] *******************************************************
2025-07-17 15:55:36,068 p=77341 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 15:55:36,662 p=77341 u=daddasoft n=ansible | TASK [sync_produit : Send Slack notification - Starting sync process] **********************************************
2025-07-17 15:55:36,662 p=77341 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 15:55:36,700 p=77341 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:55:36,700 p=77341 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:55:36,700 p=77341 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:55:40,138 p=77341 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password (Async)] *********************************************
2025-07-17 15:55:40,139 p=77341 u=daddasoft n=ansible | failed: [localhost -> vps5] (item={'key': 'pharmalien.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "item": {"key": "pharmalien.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}
2025-07-17 15:55:40,631 p=77341 u=daddasoft n=ansible | failed: [localhost -> vps5] (item={'key': 'webfixgroupe.dev', 'value': 'vps5'}) => {"ansible_loop_var": "item", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}
2025-07-17 15:55:40,631 p=77341 u=daddasoft n=ansible | fatal: [localhost -> {{ item.value }}]: UNREACHABLE! => {"changed": false, "msg": "All items completed", "results": [{"ansible_loop_var": "item", "item": {"key": "pharmalien.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}, {"ansible_loop_var": "item", "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "msg": "Failed to connect to the host via ssh: ubuntu@**************: Permission denied (publickey,password).", "unreachable": true}], "unreachable": true}
2025-07-17 15:55:40,633 p=77341 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:55:40,633 p=77341 u=daddasoft n=ansible | localhost                  : ok=4    changed=1    unreachable=1    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 15:56:22,403 p=77555 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:56:22,406 p=77555 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:56:22,479 p=77555 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:56:22,510 p=77555 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:56:22,524 p=77555 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:56:22,545 p=77555 u=daddasoft n=ansible | TASK [sync_produit : Set Slack webhook URL from environment] *******************************************************
2025-07-17 15:56:22,546 p=77555 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 15:56:23,102 p=77555 u=daddasoft n=ansible | TASK [sync_produit : Send Slack notification - Starting sync process] **********************************************
2025-07-17 15:56:23,102 p=77555 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 15:56:23,141 p=77555 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:56:23,142 p=77555 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:56:23,142 p=77555 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:56:28,395 p=77555 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password (Async)] *********************************************
2025-07-17 15:56:28,395 p=77555 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:56:30,984 p=77555 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:56:30,986 p=77555 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:56:31,191 p=77555 u=daddasoft n=ansible | TASK [sync_produit : Wait for async jobs to complete] **************************************************************
2025-07-17 15:56:31,192 p=77555 u=daddasoft n=ansible | failed: [localhost] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j225132288984.1069354', 'results_file': '/home/<USER>/.ansible_async/j225132288984.1069354', 'changed': True, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j225132288984.1069354", "ansible_loop_var": "item", "attempts": 1, "changed": false, "finished": 1, "item": {"ansible_job_id": "j225132288984.1069354", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "pharmalien.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j225132288984.1069354", "started": 1}, "msg": "could not find job", "results_file": "/home/<USER>/.ansible_async/j225132288984.1069354", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 15:56:31,302 p=77555 u=daddasoft n=ansible | failed: [localhost] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j557329627120.1069412', 'results_file': '/home/<USER>/.ansible_async/j557329627120.1069412', 'changed': True, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j557329627120.1069412", "ansible_loop_var": "item", "attempts": 1, "changed": false, "finished": 1, "item": {"ansible_job_id": "j557329627120.1069412", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j557329627120.1069412", "started": 1}, "msg": "could not find job", "results_file": "/home/<USER>/.ansible_async/j557329627120.1069412", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 15:56:31,304 p=77555 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:56:31,304 p=77555 u=daddasoft n=ansible | localhost                  : ok=5    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 15:58:05,542 p=77979 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:58:05,546 p=77979 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:58:05,642 p=77979 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:58:05,685 p=77979 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:58:05,701 p=77979 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:58:05,727 p=77979 u=daddasoft n=ansible | TASK [sync_produit : Set Slack webhook URL from environment] *******************************************************
2025-07-17 15:58:05,727 p=77979 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 15:58:06,301 p=77979 u=daddasoft n=ansible | TASK [sync_produit : Send Slack notification - Starting sync process] **********************************************
2025-07-17 15:58:06,302 p=77979 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 15:58:06,343 p=77979 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 15:58:06,344 p=77979 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:58:06,344 p=77979 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:58:13,210 p=77979 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password (Async)] *********************************************
2025-07-17 15:58:13,210 p=77979 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 15:58:15,880 p=77979 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 15:58:15,882 p=77979 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 15:58:52,428 p=77979 u=daddasoft n=ansible | TASK [sync_produit : Wait for async jobs to complete] **************************************************************
2025-07-17 15:58:52,428 p=77979 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j913402938778.1070516', 'results_file': '/home/<USER>/.ansible_async/j913402938778.1070516', 'changed': True, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'})
2025-07-17 15:58:53,456 p=77979 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j291159724919.1070562', 'results_file': '/home/<USER>/.ansible_async/j291159724919.1070562', 'changed': True, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'})
2025-07-17 15:58:53,477 p=77979 u=daddasoft n=ansible | TASK [sync_produit : Collect all job results] **********************************************************************
2025-07-17 15:58:53,477 p=77979 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 15:58:53,508 p=77979 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 15:58:53,508 p=77979 u=daddasoft n=ansible | ok: [localhost] => (item=pharmalien.dev) => {
    "msg": "pharmalien.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:58:53,513 p=77979 u=daddasoft n=ansible | ok: [localhost] => (item=webfixgroupe.dev) => {
    "msg": "webfixgroupe.dev on vps5 ✅ SUCCESS"
}
2025-07-17 15:58:53,541 p=77979 u=daddasoft n=ansible | TASK [sync_produit : Prepare Slack message for results] ************************************************************
2025-07-17 15:58:53,542 p=77979 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 15:58:54,048 p=77979 u=daddasoft n=ansible | TASK [sync_produit : Send Slack notification with results] *********************************************************
2025-07-17 15:58:54,048 p=77979 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 15:58:54,054 p=77979 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 15:58:54,054 p=77979 u=daddasoft n=ansible | localhost                  : ok=10   changed=3    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-17 15:59:59,856 p=78632 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 15:59:59,859 p=78632 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 15:59:59,931 p=78632 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 15:59:59,962 p=78632 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 15:59:59,976 p=78632 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 15:59:59,997 p=78632 u=daddasoft n=ansible | TASK [sync_produit : Set Slack webhook URL from environment] *******************************************************
2025-07-17 15:59:59,998 p=78632 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:00:00,598 p=78632 u=daddasoft n=ansible | TASK [sync_produit : Send Slack notification - Starting sync process] **********************************************
2025-07-17 16:00:00,598 p=78632 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:00:00,638 p=78632 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 16:00:00,638 p=78632 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 16:00:00,638 p=78632 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 16:00:05,591 p=78632 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password (Async)] *********************************************
2025-07-17 16:00:05,592 p=78632 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 16:00:08,325 p=78632 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 16:00:08,326 p=78632 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 16:00:44,841 p=78632 u=daddasoft n=ansible | TASK [sync_produit : Wait for async jobs to complete] **************************************************************
2025-07-17 16:00:44,841 p=78632 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j525310583420.1071232', 'results_file': '/home/<USER>/.ansible_async/j525310583420.1071232', 'changed': True, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'})
2025-07-17 16:00:45,892 p=78632 u=daddasoft n=ansible | failed: [localhost -> vps5(**************)] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j970249958346.1071337', 'results_file': '/home/<USER>/.ansible_async/j970249958346.1071337', 'changed': True, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j970249958346.1071337", "ansible_loop_var": "item", "attempts": 1, "changed": true, "cmd": "exit 1", "delta": "0:00:00.023406", "end": "2025-07-17 15:00:10.756934", "finished": 1, "item": {"ansible_job_id": "j970249958346.1071337", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j970249958346.1071337", "started": 1}, "msg": "non-zero return code", "rc": 1, "results_file": "/home/<USER>/.ansible_async/j970249958346.1071337", "start": "2025-07-17 15:00:10.733528", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 16:00:45,894 p=78632 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 16:00:45,894 p=78632 u=daddasoft n=ansible | localhost                  : ok=5    changed=2    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-17 16:01:44,862 p=79231 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 16:01:44,865 p=79231 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 16:01:44,938 p=79231 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 16:01:44,969 p=79231 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 16:01:44,984 p=79231 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 16:01:45,004 p=79231 u=daddasoft n=ansible | TASK [sync_produit : Set Slack webhook URL from environment] *******************************************************
2025-07-17 16:01:45,004 p=79231 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:01:45,625 p=79231 u=daddasoft n=ansible | TASK [sync_produit : Send Slack notification - Starting sync process] **********************************************
2025-07-17 16:01:45,625 p=79231 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:01:45,682 p=79231 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 16:01:45,682 p=79231 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 16:01:45,682 p=79231 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 16:01:48,200 p=79231 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password (Async)] *********************************************
2025-07-17 16:01:48,201 p=79231 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 16:01:50,471 p=79231 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 16:01:50,473 p=79231 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 16:02:27,364 p=79231 u=daddasoft n=ansible | TASK [sync_produit : Wait for async jobs to complete] **************************************************************
2025-07-17 16:02:27,364 p=79231 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j405842853458.1071678', 'results_file': '/home/<USER>/.ansible_async/j405842853458.1071678', 'changed': True, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'})
2025-07-17 16:02:28,330 p=79231 u=daddasoft n=ansible | failed: [localhost -> vps5(**************)] (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j713548984570.1071748', 'results_file': '/home/<USER>/.ansible_async/j713548984570.1071748', 'changed': True, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'}) => {"ansible_job_id": "j713548984570.1071748", "ansible_loop_var": "item", "attempts": 1, "changed": true, "cmd": "exit 1", "delta": "0:00:00.008331", "end": "2025-07-17 15:01:52.868207", "finished": 1, "item": {"ansible_job_id": "j713548984570.1071748", "ansible_loop_var": "item", "changed": true, "failed": 0, "finished": 0, "item": {"key": "webfixgroupe.dev", "value": "vps5"}, "results_file": "/home/<USER>/.ansible_async/j713548984570.1071748", "started": 1}, "msg": "non-zero return code", "rc": 1, "results_file": "/home/<USER>/.ansible_async/j713548984570.1071748", "start": "2025-07-17 15:01:52.859876", "started": 1, "stderr": "", "stderr_lines": [], "stdout": "", "stdout_lines": []}
2025-07-17 16:02:28,332 p=79231 u=daddasoft n=ansible | ...ignoring
2025-07-17 16:02:28,353 p=79231 u=daddasoft n=ansible | TASK [sync_produit : Collect all job results] **********************************************************************
2025-07-17 16:02:28,353 p=79231 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:02:28,383 p=79231 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 16:02:28,383 p=79231 u=daddasoft n=ansible | ok: [localhost] => (item=pharmalien.dev) => {
    "msg": "pharmalien.dev on vps5 ✅ SUCCESS"
}
2025-07-17 16:02:28,389 p=79231 u=daddasoft n=ansible | ok: [localhost] => (item=webfixgroupe.dev) => {
    "msg": "webfixgroupe.dev on vps5 ❌ FAILED"
}
2025-07-17 16:02:28,422 p=79231 u=daddasoft n=ansible | TASK [sync_produit : Prepare Slack message for results] ************************************************************
2025-07-17 16:02:28,423 p=79231 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:02:28,925 p=79231 u=daddasoft n=ansible | TASK [sync_produit : Send Slack notification with results] *********************************************************
2025-07-17 16:02:28,925 p=79231 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:02:28,932 p=79231 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 16:02:28,932 p=79231 u=daddasoft n=ansible | localhost                  : ok=10   changed=3    unreachable=0    failed=0    skipped=0    rescued=0    ignored=1   
2025-07-17 16:11:23,166 p=81197 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-17 16:11:23,172 p=81197 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly 
instead. This feature will be removed in version 2.20. Deprecation warnings can be disabled by setting 
deprecation_warnings=False in ansible.cfg.
2025-07-17 16:11:23,320 p=81197 u=daddasoft n=ansible | PLAY [Setup with parameters] ***************************************************************************************
2025-07-17 16:11:23,389 p=81197 u=daddasoft n=ansible | TASK [Include role with vars] **************************************************************************************
2025-07-17 16:11:23,408 p=81197 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-17 16:11:23,433 p=81197 u=daddasoft n=ansible | TASK [sync_produit : Set Slack webhook URL from environment] *******************************************************
2025-07-17 16:11:23,433 p=81197 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:11:24,070 p=81197 u=daddasoft n=ansible | TASK [sync_produit : Send Slack notification - Starting sync process] **********************************************
2025-07-17 16:11:24,071 p=81197 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:11:24,115 p=81197 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] ******************************************************
2025-07-17 16:11:24,115 p=81197 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 16:11:24,115 p=81197 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 16:11:24,115 p=81197 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'lacentrale.preprod', 'value': 'vps5'})
2025-07-17 16:11:28,808 p=81197 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password (Async)] *********************************************
2025-07-17 16:11:28,808 p=81197 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-17 16:11:31,079 p=81197 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-17 16:11:33,436 p=81197 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'lacentrale.preprod', 'value': 'vps5'})
2025-07-17 16:11:33,438 p=81197 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but
future installation of another Python interpreter could change the meaning of that path. See
https://docs.ansible.com/ansible-core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-17 16:12:11,394 p=81197 u=daddasoft n=ansible | TASK [sync_produit : Wait for async jobs to complete] **************************************************************
2025-07-17 16:12:11,394 p=81197 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j475398835756.1072554', 'results_file': '/home/<USER>/.ansible_async/j475398835756.1072554', 'changed': True, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'})
2025-07-17 16:12:12,418 p=81197 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j804299675282.1072642', 'results_file': '/home/<USER>/.ansible_async/j804299675282.1072642', 'changed': True, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'})
2025-07-17 16:12:38,416 p=81197 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j121839131718.1072879', 'results_file': '/home/<USER>/.ansible_async/j121839131718.1072879', 'changed': True, 'item': {'key': 'lacentrale.preprod', 'value': 'vps5'}, 'ansible_loop_var': 'item'})
2025-07-17 16:12:38,444 p=81197 u=daddasoft n=ansible | TASK [sync_produit : Collect all job results] **********************************************************************
2025-07-17 16:12:38,444 p=81197 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:12:38,479 p=81197 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] *********************************************************************
2025-07-17 16:12:38,479 p=81197 u=daddasoft n=ansible | ok: [localhost] => (item=pharmalien.dev) => {
    "msg": "pharmalien.dev on vps5 ✅ SUCCESS"
}
2025-07-17 16:12:38,483 p=81197 u=daddasoft n=ansible | ok: [localhost] => (item=webfixgroupe.dev) => {
    "msg": "webfixgroupe.dev on vps5 ✅ SUCCESS"
}
2025-07-17 16:12:38,487 p=81197 u=daddasoft n=ansible | ok: [localhost] => (item=lacentrale.preprod) => {
    "msg": "lacentrale.preprod on vps5 ✅ SUCCESS"
}
2025-07-17 16:12:38,515 p=81197 u=daddasoft n=ansible | TASK [sync_produit : Prepare Slack message for results] ************************************************************
2025-07-17 16:12:38,516 p=81197 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:12:39,001 p=81197 u=daddasoft n=ansible | TASK [sync_produit : Send Slack notification with results] *********************************************************
2025-07-17 16:12:39,001 p=81197 u=daddasoft n=ansible | ok: [localhost]
2025-07-17 16:12:39,007 p=81197 u=daddasoft n=ansible | PLAY RECAP *********************************************************************************************************
2025-07-17 16:12:39,007 p=81197 u=daddasoft n=ansible | localhost                  : ok=10   changed=3    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-18 10:06:05,371 p=2620 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-18 10:06:05,377 p=2620 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be 
removed in version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-18 10:06:05,529 p=2620 u=daddasoft n=ansible | PLAY [Setup with parameters] ******************************************************************************************************************
2025-07-18 10:06:05,578 p=2620 u=daddasoft n=ansible | TASK [Include role with vars] *****************************************************************************************************************
2025-07-18 10:06:05,590 p=2620 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-18 10:06:05,612 p=2620 u=daddasoft n=ansible | TASK [sync_produit : Set Slack webhook URL from environment] **********************************************************************************
2025-07-18 10:06:05,612 p=2620 u=daddasoft n=ansible | ok: [localhost]
2025-07-18 10:06:06,647 p=2620 u=daddasoft n=ansible | TASK [sync_produit : Send Slack notification - Starting sync process] *************************************************************************
2025-07-18 10:06:06,647 p=2620 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.10, but future installation of
another Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-18 10:06:06,648 p=2620 u=daddasoft n=ansible | ok: [localhost]
2025-07-18 10:06:06,692 p=2620 u=daddasoft n=ansible | TASK [sync_produit : Register host with dynamic SSH password] *********************************************************************************
2025-07-18 10:06:06,692 p=2620 u=daddasoft n=ansible | changed: [localhost] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-18 10:06:06,693 p=2620 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-18 10:06:06,693 p=2620 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'pharmalien.preprod', 'value': 'vps5'})
2025-07-18 10:06:06,693 p=2620 u=daddasoft n=ansible | ok: [localhost] => (item={'key': 'lacentrale.preprod', 'value': 'vps5'})
2025-07-18 10:06:11,413 p=2620 u=daddasoft n=ansible | TASK [sync_produit : Execute script on each VPS with password (Async)] ************************************************************************
2025-07-18 10:06:11,413 p=2620 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.dev', 'value': 'vps5'})
2025-07-18 10:06:13,686 p=2620 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'webfixgroupe.dev', 'value': 'vps5'})
2025-07-18 10:06:16,005 p=2620 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'pharmalien.preprod', 'value': 'vps5'})
2025-07-18 10:06:18,378 p=2620 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'key': 'lacentrale.preprod', 'value': 'vps5'})
2025-07-18 10:06:18,379 p=2620 u=daddasoft n=ansible | [WARNING]: Platform linux on host localhost is using the discovered Python interpreter at /usr/bin/python3.12, but future installation of
another Python interpreter could change the meaning of that path. See https://docs.ansible.com/ansible-
core/2.17/reference_appendices/interpreter_discovery.html for more information.

2025-07-18 10:06:41,773 p=2620 u=daddasoft n=ansible | TASK [sync_produit : Wait for async jobs to complete] *****************************************************************************************
2025-07-18 10:06:41,774 p=2620 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j593993497872.1133929', 'results_file': '/home/<USER>/.ansible_async/j593993497872.1133929', 'changed': True, 'item': {'key': 'pharmalien.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'})
2025-07-18 10:06:42,771 p=2620 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j962589368916.1134034', 'results_file': '/home/<USER>/.ansible_async/j962589368916.1134034', 'changed': True, 'item': {'key': 'webfixgroupe.dev', 'value': 'vps5'}, 'ansible_loop_var': 'item'})
2025-07-18 10:06:54,332 p=2620 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j664046330745.1134312', 'results_file': '/home/<USER>/.ansible_async/j664046330745.1134312', 'changed': True, 'item': {'key': 'pharmalien.preprod', 'value': 'vps5'}, 'ansible_loop_var': 'item'})
2025-07-18 10:06:55,160 p=2620 u=daddasoft n=ansible | changed: [localhost -> vps5(**************)] => (item={'failed': 0, 'started': 1, 'finished': 0, 'ansible_job_id': 'j837643856711.1134530', 'results_file': '/home/<USER>/.ansible_async/j837643856711.1134530', 'changed': True, 'item': {'key': 'lacentrale.preprod', 'value': 'vps5'}, 'ansible_loop_var': 'item'})
2025-07-18 10:06:55,190 p=2620 u=daddasoft n=ansible | TASK [sync_produit : Collect all job results] *************************************************************************************************
2025-07-18 10:06:55,191 p=2620 u=daddasoft n=ansible | ok: [localhost]
2025-07-18 10:06:55,220 p=2620 u=daddasoft n=ansible | TASK [sync_produit : Show task result per app] ************************************************************************************************
2025-07-18 10:06:55,221 p=2620 u=daddasoft n=ansible | ok: [localhost] => (item=pharmalien.dev) => {
    "msg": "pharmalien.dev on vps5 ✅ SUCCESS"
}
2025-07-18 10:06:55,225 p=2620 u=daddasoft n=ansible | ok: [localhost] => (item=webfixgroupe.dev) => {
    "msg": "webfixgroupe.dev on vps5 ✅ SUCCESS"
}
2025-07-18 10:06:55,230 p=2620 u=daddasoft n=ansible | ok: [localhost] => (item=pharmalien.preprod) => {
    "msg": "pharmalien.preprod on vps5 ✅ SUCCESS"
}
2025-07-18 10:06:55,236 p=2620 u=daddasoft n=ansible | ok: [localhost] => (item=lacentrale.preprod) => {
    "msg": "lacentrale.preprod on vps5 ✅ SUCCESS"
}
2025-07-18 10:06:55,263 p=2620 u=daddasoft n=ansible | TASK [sync_produit : Prepare Slack message for results] ***************************************************************************************
2025-07-18 10:06:55,263 p=2620 u=daddasoft n=ansible | ok: [localhost]
2025-07-18 10:06:55,711 p=2620 u=daddasoft n=ansible | TASK [sync_produit : Send Slack notification with results] ************************************************************************************
2025-07-18 10:06:55,711 p=2620 u=daddasoft n=ansible | ok: [localhost]
2025-07-18 10:06:55,713 p=2620 u=daddasoft n=ansible | PLAY RECAP ************************************************************************************************************************************
2025-07-18 10:06:55,713 p=2620 u=daddasoft n=ansible | localhost                  : ok=10   changed=3    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-18 10:23:33,050 p=5654 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-18 10:23:33,059 p=5654 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be 
removed in version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-18 10:23:33,188 p=5654 u=daddasoft n=ansible | ERROR! conflicting action statements: set_fact, selected_apps

The error appears to be in '/home/<USER>/play-deomo/playbopoks/main.yaml': line 8, column 5, but may
be elsewhere in the file depending on the exact syntax problem.

The offending line appears to be:


  - name: set selected Apps Based on target_app
    ^ here

2025-07-18 10:23:45,810 p=5766 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-18 10:23:45,813 p=5766 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be 
removed in version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-18 10:23:45,880 p=5766 u=daddasoft n=ansible | ERROR! conflicting action statements: set_fact, selected_apps

The error appears to be in '/home/<USER>/play-deomo/playbopoks/main.yaml': line 8, column 5, but may
be elsewhere in the file depending on the exact syntax problem.

The offending line appears to be:


  - name: set selected Apps Based on target_app
    ^ here

2025-07-18 10:23:56,881 p=5865 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-18 10:23:56,885 p=5865 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be 
removed in version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-18 10:23:57,013 p=5865 u=daddasoft n=ansible | PLAY [Setup with parameters] ******************************************************************************************************************
2025-07-18 10:23:57,035 p=5865 u=daddasoft n=ansible | TASK [set selected Apps Based on target_app] **************************************************************************************************
2025-07-18 10:23:57,035 p=5865 u=daddasoft n=ansible | fatal: [localhost]: FAILED! => {"msg": "The task includes an option with an undefined variable.. 'target_app' is undefined\n\nThe error appears to be in '/home/<USER>/play-deomo/playbopoks/main.yaml': line 8, column 5, but may\nbe elsewhere in the file depending on the exact syntax problem.\n\nThe offending line appears to be:\n\n\n  - name: set selected Apps Based on target_app\n    ^ here\n"}
2025-07-18 10:23:57,036 p=5865 u=daddasoft n=ansible | PLAY RECAP ************************************************************************************************************************************
2025-07-18 10:23:57,036 p=5865 u=daddasoft n=ansible | localhost                  : ok=0    changed=0    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-18 10:24:24,397 p=6044 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-18 10:24:24,401 p=6044 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be 
removed in version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-18 10:24:24,492 p=6044 u=daddasoft n=ansible | PLAY [Setup with parameters] ******************************************************************************************************************
2025-07-18 10:24:24,508 p=6044 u=daddasoft n=ansible | TASK [set selected Apps Based on target_app] **************************************************************************************************
2025-07-18 10:24:24,509 p=6044 u=daddasoft n=ansible | fatal: [localhost]: FAILED! => {"msg": "The task includes an option with an undefined variable.. 'prod_sync_app' is undefined\n\nThe error appears to be in '/home/<USER>/play-deomo/playbopoks/main.yaml': line 8, column 5, but may\nbe elsewhere in the file depending on the exact syntax problem.\n\nThe offending line appears to be:\n\n\n  - name: set selected Apps Based on target_app\n    ^ here\n"}
2025-07-18 10:24:24,509 p=6044 u=daddasoft n=ansible | PLAY RECAP ************************************************************************************************************************************
2025-07-18 10:24:24,509 p=6044 u=daddasoft n=ansible | localhost                  : ok=0    changed=0    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-18 10:25:47,192 p=6476 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-18 10:25:47,195 p=6476 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be 
removed in version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-18 10:25:47,276 p=6476 u=daddasoft n=ansible | PLAY [Setup with parameters] ******************************************************************************************************************
2025-07-18 10:25:47,310 p=6476 u=daddasoft n=ansible | TASK [Include role with vars] *****************************************************************************************************************
2025-07-18 10:25:47,320 p=6476 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-18 10:25:47,336 p=6476 u=daddasoft n=ansible | TASK [sync_produit : set selected Apps Based on target_app] ***********************************************************************************
2025-07-18 10:25:47,337 p=6476 u=daddasoft n=ansible | ok: [localhost]
2025-07-18 10:25:47,356 p=6476 u=daddasoft n=ansible | TASK [sync_produit : debug Selected Apps] *****************************************************************************************************
2025-07-18 10:25:47,357 p=6476 u=daddasoft n=ansible | ok: [localhost] => {
    "selected_apps": {
        "lacentrale.prod": "vps9",
        "pharmalien.prod": "vps7",
        "webfixgroupe.prod": "vps9",
        "wingroupe.prod": "vps10"
    }
}
2025-07-18 10:25:47,359 p=6476 u=daddasoft n=ansible | PLAY RECAP ************************************************************************************************************************************
2025-07-18 10:25:47,360 p=6476 u=daddasoft n=ansible | localhost                  : ok=3    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-18 10:25:56,886 p=6555 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-18 10:25:56,890 p=6555 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be 
removed in version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-18 10:25:56,965 p=6555 u=daddasoft n=ansible | PLAY [Setup with parameters] ******************************************************************************************************************
2025-07-18 10:25:56,995 p=6555 u=daddasoft n=ansible | TASK [Include role with vars] *****************************************************************************************************************
2025-07-18 10:25:57,002 p=6555 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-18 10:25:57,015 p=6555 u=daddasoft n=ansible | TASK [sync_produit : set selected Apps Based on target_app] ***********************************************************************************
2025-07-18 10:25:57,015 p=6555 u=daddasoft n=ansible | fatal: [localhost]: FAILED! => {"msg": "The task includes an option with an undefined variable.. 'apps_to_vps' is undefined\n\nThe error appears to be in '/home/<USER>/play-deomo/roles/sync_produit/tasks/main.yml': line 3, column 5, but may\nbe elsewhere in the file depending on the exact syntax problem.\n\nThe offending line appears to be:\n\n\n  - name: set selected Apps Based on target_app\n    ^ here\n"}
2025-07-18 10:25:57,016 p=6555 u=daddasoft n=ansible | PLAY RECAP ************************************************************************************************************************************
2025-07-18 10:25:57,016 p=6555 u=daddasoft n=ansible | localhost                  : ok=1    changed=0    unreachable=0    failed=1    skipped=0    rescued=0    ignored=0   
2025-07-18 10:26:18,362 p=6712 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-18 10:26:18,365 p=6712 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be 
removed in version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-18 10:26:18,443 p=6712 u=daddasoft n=ansible | PLAY [Setup with parameters] ******************************************************************************************************************
2025-07-18 10:26:18,472 p=6712 u=daddasoft n=ansible | TASK [Include role with vars] *****************************************************************************************************************
2025-07-18 10:26:18,479 p=6712 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-18 10:26:18,495 p=6712 u=daddasoft n=ansible | TASK [sync_produit : set selected Apps Based on target_app] ***********************************************************************************
2025-07-18 10:26:18,495 p=6712 u=daddasoft n=ansible | ok: [localhost]
2025-07-18 10:26:18,513 p=6712 u=daddasoft n=ansible | TASK [sync_produit : debug Selected Apps] *****************************************************************************************************
2025-07-18 10:26:18,513 p=6712 u=daddasoft n=ansible | ok: [localhost] => {
    "selected_apps": {
        "lacentrale.preprod": "vps5",
        "pharmalien.dev": "vps5",
        "pharmalien.preprod": "vps5",
        "webfixgroupe.dev": "vps5"
    }
}
2025-07-18 10:26:18,516 p=6712 u=daddasoft n=ansible | PLAY RECAP ************************************************************************************************************************************
2025-07-18 10:26:18,516 p=6712 u=daddasoft n=ansible | localhost                  : ok=3    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-18 10:26:57,364 p=6882 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-18 10:26:57,367 p=6882 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be 
removed in version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-18 10:26:57,437 p=6882 u=daddasoft n=ansible | PLAY [Setup with parameters] ******************************************************************************************************************
2025-07-18 10:26:57,463 p=6882 u=daddasoft n=ansible | TASK [Include role with vars] *****************************************************************************************************************
2025-07-18 10:26:57,470 p=6882 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-18 10:26:57,484 p=6882 u=daddasoft n=ansible | TASK [sync_produit : set selected Apps Based on target_app] ***********************************************************************************
2025-07-18 10:26:57,484 p=6882 u=daddasoft n=ansible | ok: [localhost]
2025-07-18 10:26:57,498 p=6882 u=daddasoft n=ansible | TASK [sync_produit : debug Selected Apps] *****************************************************************************************************
2025-07-18 10:26:57,498 p=6882 u=daddasoft n=ansible | ok: [localhost] => {
    "selected_apps": {
        "pharmalien.dev": "unknown"
    }
}
2025-07-18 10:26:57,500 p=6882 u=daddasoft n=ansible | PLAY RECAP ************************************************************************************************************************************
2025-07-18 10:26:57,500 p=6882 u=daddasoft n=ansible | localhost                  : ok=3    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
2025-07-18 10:27:42,796 p=7152 u=daddasoft n=ansible | [WARNING]: Found both group and host with same name: vps5

2025-07-18 10:27:42,799 p=7152 u=daddasoft n=ansible | [DEPRECATION WARNING]: The 'smart' option for connections is deprecated. Set the connection plugin directly instead. This feature will be 
removed in version 2.20. Deprecation warnings can be disabled by setting deprecation_warnings=False in ansible.cfg.
2025-07-18 10:27:42,869 p=7152 u=daddasoft n=ansible | PLAY [Setup with parameters] ******************************************************************************************************************
2025-07-18 10:27:42,898 p=7152 u=daddasoft n=ansible | TASK [Include role with vars] *****************************************************************************************************************
2025-07-18 10:27:42,906 p=7152 u=daddasoft n=ansible | included: sync_produit for localhost
2025-07-18 10:27:42,921 p=7152 u=daddasoft n=ansible | TASK [sync_produit : set selected Apps Based on target_app] ***********************************************************************************
2025-07-18 10:27:42,922 p=7152 u=daddasoft n=ansible | ok: [localhost]
2025-07-18 10:27:42,936 p=7152 u=daddasoft n=ansible | TASK [sync_produit : debug Selected Apps] *****************************************************************************************************
2025-07-18 10:27:42,936 p=7152 u=daddasoft n=ansible | ok: [localhost] => {
    "selected_apps": {
        "webfixgroupe.dev": "unknown"
    }
}
2025-07-18 10:27:42,938 p=7152 u=daddasoft n=ansible | PLAY RECAP ************************************************************************************************************************************
2025-07-18 10:27:42,938 p=7152 u=daddasoft n=ansible | localhost                  : ok=3    changed=0    unreachable=0    failed=0    skipped=0    rescued=0    ignored=0   
