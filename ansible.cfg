[defaults]
inventory = ./inventory
host_key_checking = false
remote_user = ubuntu
timeout = 30
forks = 10 
poll_interval = 15
transport = smart

#logs
log_path = logs/ansible.log
display_skipped_hosts = False
display_ok_hosts = True
# stdout_callback = yaml
# bin_ansible_callbacks = True


#Performance
gathering = smart
fact_caching = jsonfile
fact_caching_connection = /tmp/ansible_facts_cache
fact_caching_timeout = 86400
pipelining = True
control_path_dir = /tmp/.ansible-cp

roles_path = ./roles
