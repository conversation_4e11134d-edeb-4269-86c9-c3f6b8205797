app_to_vps:
  pharmalien.dev: vps5
  webfixgroupe.dev: vps5
  pharmalien.preprod: vps5
  lacentrale.preprod: vps5



path_to_script:
  webfixgroupe.dev: /home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_webfixgroupe_dev/syncprd/import_webfixgroupe_syncprd.sh
  pharmalien.dev: /home/<USER>/docker_dir/shared_local_volumes/dev/postgres.dev/sync_pharmalien_dev/syncprd/import_pharmahub_syncprd.sh
  pharmalien.preprod: /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/sync_pharmalien_preprod/syncprd/import_pharmahub_syncprd.sh
  lacentrale.preprod: /home/<USER>/docker_dir/shared_local_volumes/preprod/postgres.preprod/achat_groupe/syncprd/import_pharmahub_syncprd.sh
  lacentrale.prod: /home/<USER>/docker_dir/shared_local_volumes/prod/postgres.prod/achat_groupe/syncprd/import_pharmahub_syncprd.sh
  pharmalien.prod: /home/<USER>/docker_dir/shared_local_volumes/prod/postgres.prod/sync_produit_win_produit/syncprd/import_pharmahub_syncprd.sh
  webfixgroupe.prod: /home/<USER>/docker_dir/shared_local_volumes/prod/postgres.prod/webfixgroupe/sync_db_winproduit/syncprd/import_webfixgroupe_syncprd.sh
  wingroupe.prod: /home/<USER>/docker_dir/shared_local_volumes/prod/postgres.prod/wingroupe/sync/import_wingroupe_syncprd.sh



prod_sync_app:
  lacentrale.prod: vps9
  wingroupe.prod: vps10
  pharmalien.prod: vps7
  webfixgroupe.prod : vps9



vps_to_ip:
  vps5: **************
  vps6: ************
  vps7: **************
  vps8: **************
  vps9: ***************
  vps10: *************
  vps11: *************