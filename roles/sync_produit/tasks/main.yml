---

  - name: set selected Apps Based on target_app
    set_fact:
      selected_apps: "{{ prod_sync_app if target_app == 'all.prod' else app_to_vps if target_app == 'all.dev' else {target_app: prod_sync_app.get(target_app, 'unknown')} }}"

  - name: debug Selected Apps
    debug:
      var: selected_apps

# - name: Set Slack webhook URL from environment
#   set_fact:
#     slack_webhook_url: "{{ lookup('env', 'SLACK_WEBHOOK_URL') }}"
#   when: lookup('env', 'SLACK_WEBHOOK_URL') != ""

# - name: Send Slack notification - Starting sync process
#   uri:
#     url: "{{ slack_webhook_url }}"
#     method: POST
#     body_format: json
#     body:
#       text: "🚀 Starting sync process for {{ app_to_vps.keys() | list | join(', ') }}"
#       username: "Ansible Sync Bot"
#       icon_emoji: ":robot_face:"
#   when: slack_webhook_url is defined
#   ignore_errors: yes

# - name: Register host with dynamic SSH password
#   add_host:
#     name: "{{ item.value }}"
#     ansible_host: "{{vps_to_ip[item.value]}}"
#     ansible_user: ubuntu
#     ansible_ssh_pass: "{{ lookup('env', 'SSH_' + item.value | upper + '_PASS') }}"
#     ansible_ssh_common_args: "-o Port=8700"
#   loop: "{{ app_to_vps | dict2items }}"

# - name: Execute script on each VPS with password (Async)
#   ansible.builtin.shell: "{{ path_to_script[item.key] | default('echo \"Not Found\"') }}"
#   delegate_to: "{{ item.value }}"
#   loop: "{{ app_to_vps | dict2items }}"
#   loop_control:
#     loop_var: item
#   when: path_to_script[item.key] is defined
#   ignore_errors: yes
#   async: 300  # 5 minutes timeout
#   poll: 0     # Don't wait, run async
#   register: async_jobs

# - name: Wait for async jobs to complete
#   async_status:
#     jid: "{{ item.ansible_job_id }}"
#   delegate_to: "{{ item.item.value }}"
#   register: job_status
#   until: job_status.finished
#   ignore_errors: yes
#   retries: 60  # Check every 10 seconds for up to 10 minutes
#   delay: 10
#   loop: "{{ async_jobs.results }}"
#   when: item.ansible_job_id is defined

# - name: Collect all job results
#   set_fact:
#     script_results: "{{ job_status }}"

# - name: Show task result per app
#   ansible.builtin.debug:
#     msg: >-
#       {{
#         item.item.item.key ~ " on " ~ item.item.item.value ~
#         (item.failed | default(false) | ternary(" ❌ FAILED", " ✅ SUCCESS"))
#       }}
#     verbosity: 0
#   loop: "{{ script_results.results }}"
#   loop_control:
#     label: "{{ item.item.item.key }}"

# - name: Prepare Slack message for results
#   set_fact:
#     slack_message: |
#       📊 *Sync Process Results*
#       {% for result in script_results.results %}
#       {% set app_name = result.item.item.key %}
#       {% set vps_name = result.item.item.value %}
#       {% set status = "✅ SUCCESS" if not (result.failed | default(false)) else "❌ FAILED" %}
#       • {{ app_name }} on {{ vps_name }}: {{ status }}
#       {% endfor %}

# - name: Send Slack notification with results
#   uri:
#     url: "{{ slack_webhook_url }}"
#     method: POST
#     body_format: json
#     body:
#       text: "{{ slack_message }}"
#       username: "Ansible Sync Bot"
#       icon_emoji: ":chart_with_upwards_trend:"
#   when: slack_webhook_url is defined
#   ignore_errors: yes
