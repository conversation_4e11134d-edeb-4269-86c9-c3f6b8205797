# Async Sync with Slack Notifications

This Ansible playbook now supports asynchronous parallel execution with Slack notifications.

## Features

### 🚀 Asynchronous Parallel Execution
- Scripts run in parallel across all VPS instances
- No waiting for one script to finish before starting the next
- Configurable timeout (default: 5 minutes per script)
- Automatic status checking every 10 seconds

### 📱 Slack Notifications
- Start notification when sync process begins
- Detailed results notification when all scripts complete
- Success/failure status for each app
- Customizable bot name and emoji

## Setup

### 1. Slack Webhook Setup
```bash
# Run the setup script
source setup_slack_env.sh

# Or manually set the environment variable
export SLACK_WEBHOOK_URL='https://hooks.slack.com/services/YOUR/WEBHOOK/URL'
```

### 2. Create Slack Incoming Webhook
1. Go to your Slack workspace
2. Navigate to Apps → Incoming Webhooks
3. Create a new webhook
4. Choose the channel for notifications
5. Copy the webhook URL

### 3. Environment Variables Required
```bash
# Slack webhook (optional - if not set, no notifications will be sent)
export SLACK_WEBHOOK_URL='https://hooks.slack.com/services/...'

# SSH passwords for each VPS (existing requirement)
export SSH_VPS5_PASS='your_password'
export SSH_VPS6_PASS='your_password'
# ... etc for other VPS instances
```

## Usage

Run the playbook as usual:
```bash
ansible-playbook -i inventory/hosts playbopoks/main.yaml
```

## What Happens

1. **Start Notification**: Slack message sent when sync begins
2. **Parallel Execution**: All scripts start simultaneously on their respective VPS instances
3. **Status Monitoring**: Ansible checks job status every 10 seconds
4. **Results Collection**: Once all jobs complete, results are collected
5. **Final Notification**: Detailed Slack message with success/failure status for each app

## Configuration

### Timeout Settings
- **Script timeout**: 300 seconds (5 minutes) - modify `async: 300`
- **Status check retries**: 60 retries × 10 seconds = 10 minutes total - modify `retries: 60` and `delay: 10`

### Slack Message Customization
Edit the Slack notification tasks in `roles/sync_produit/tasks/main.yml`:
- Change `username` for different bot name
- Change `icon_emoji` for different emoji
- Modify message format in the `text` field

## Troubleshooting

### No Slack Notifications
- Check if `SLACK_WEBHOOK_URL` is set: `echo $SLACK_WEBHOOK_URL`
- Verify webhook URL is correct
- Check Slack app permissions

### Scripts Not Running in Parallel
- Verify `async: 300` and `poll: 0` are set in the script execution task
- Check that `async_jobs` is properly registered

### Timeout Issues
- Increase `async: 300` for longer-running scripts
- Increase `retries: 60` for longer monitoring period
- Adjust `delay: 10` for more/less frequent status checks

## Example Slack Messages

**Start Message:**
```
🚀 Starting sync process for pharmalien.dev, webfixgroupe.dev
```

**Results Message:**
```
📊 Sync Process Results
• pharmalien.dev on vps5: ✅ SUCCESS
• webfixgroupe.dev on vps5: ✅ SUCCESS
```
