#!/bin/bash

# Setup script for Slack webhook environment variable
# Usage: source setup_slack_env.sh

echo "Setting up Slack webhook environment variable..."

# Check if SLACK_WEBHOOK_URL is already set
if [ -n "$SLACK_WEBHOOK_URL" ]; then
    echo "✅ SLACK_WEBHOOK_URL is already set"
    echo "Current value: ${SLACK_WEBHOOK_URL:0:50}..."
else
    echo "❌ SLACK_WEBHOOK_URL is not set"
    echo ""
    echo "To set up Slack notifications:"
    echo "1. Go to your Slack workspace"
    echo "2. Create a new Incoming Webhook app"
    echo "3. Copy the webhook URL"
    echo "4. Export it as an environment variable:"
    echo ""
    echo "   export SLACK_WEBHOOK_URL='https://hooks.slack.com/services/YOUR/WEBHOOK/URL'"
    echo ""
    echo "5. Or add it to your ~/.bashrc or ~/.zshrc for persistence"
    echo ""
    read -p "Enter your Slack webhook URL (or press Enter to skip): " webhook_url
    
    if [ -n "$webhook_url" ]; then
        export SLACK_WEBHOOK_URL="$webhook_url"
        echo "✅ SLACK_WEBHOOK_URL has been set for this session"
        echo "To make it permanent, add this line to your ~/.bashrc or ~/.zshrc:"
        echo "export SLACK_WEBHOOK_URL='$webhook_url'"
    else
        echo "⚠️  Skipping Slack setup. Notifications will be disabled."
    fi
fi

echo ""
echo "Environment setup complete!"
