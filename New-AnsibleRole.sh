#!/bin/bash

# Check if role name is given
if [ -z "$1" ]; then
  echo "Usage: $0 <role_name>"
  exit 1
fi

ROLE_NAME=$1
BASE_DIR="roles/$ROLE_NAME"

# Folder structure
mkdir -p "$BASE_DIR"/{tasks,handlers,defaults,vars,files,templates,meta,tests,molecule/default}

# Seed files
echo "# Tasks for $ROLE_NAME" > "$BASE_DIR/tasks/main.yml"
echo "# Handlers for $ROLE_NAME" > "$BASE_DIR/handlers/main.yml"
echo "# Default vars for $ROLE_NAME" > "$BASE_DIR/defaults/main.yml"
echo "# Vars for $ROLE_NAME" > "$BASE_DIR/vars/main.yml"

cat > "$BASE_DIR/meta/main.yml" <<EOF
galaxy_info:
  author: your_name
  description: Role for $ROLE_NAME
  license: MIT
  min_ansible_version: 2.9
  platforms:
    - name: Ubuntu
      versions:
        - focal
EOF

cat > "$BASE_DIR/tests/test.yml" <<EOF
- hosts: localhost
  roles:
    - $ROLE_NAME
EOF

cat > "$BASE_DIR/molecule/default/molecule.yml" <<EOF
dependency:
  name: galaxy

platforms:
  - name: instance
    image: ubuntu:22.04
    pre_build_image: true

provisioner:
  name: ansible

verifier:
  name: ansible
EOF

echo "✅ Role '$ROLE_NAME' created under roles/$ROLE_NAME"
